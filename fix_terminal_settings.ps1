# Windows Terminal 设置修复脚本
Write-Host "Windows Terminal 设置修复工具" -ForegroundColor Green
Write-Host "===============================" -ForegroundColor Green
Write-Host ""

# 查找Windows Terminal设置文件的可能位置
$possiblePaths = @(
    "$env:LOCALAPPDATA\Packages\Microsoft.WindowsTerminal_8wekyb3d8bbwe\LocalState\settings.json",
    "$env:LOCALAPPDATA\Packages\Microsoft.WindowsTerminalPreview_8wekyb3d8bbwe\LocalState\settings.json",
    "$env:APPDATA\Microsoft\Windows Terminal\settings.json"
)

$settingsPath = $null
foreach ($path in $possiblePaths) {
    if (Test-Path $path) {
        $settingsPath = $path
        Write-Host "找到设置文件: $path" -ForegroundColor Yellow
        break
    }
}

if (-not $settingsPath) {
    Write-Host "未找到Windows Terminal设置文件" -ForegroundColor Red
    Write-Host "请确保Windows Terminal已正确安装" -ForegroundColor Red
    exit 1
}

# 备份当前设置
$backupPath = $settingsPath -replace "\.json$", "_backup_$(Get-Date -Format 'yyyyMMdd_HHmmss').json"
try {
    Copy-Item $settingsPath $backupPath
    Write-Host "已备份当前设置到: $backupPath" -ForegroundColor Green
} catch {
    Write-Host "备份失败: $($_.Exception.Message)" -ForegroundColor Yellow
}

# 读取修复后的设置
$fixedSettingsPath = "settings_fixed.json"
if (-not (Test-Path $fixedSettingsPath)) {
    Write-Host "未找到修复文件: $fixedSettingsPath" -ForegroundColor Red
    exit 1
}

# 验证JSON格式
try {
    $fixedContent = Get-Content $fixedSettingsPath -Raw | ConvertFrom-Json
    Write-Host "修复文件JSON格式验证通过" -ForegroundColor Green
} catch {
    Write-Host "修复文件JSON格式错误: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 应用修复
try {
    Copy-Item $fixedSettingsPath $settingsPath -Force
    Write-Host "设置文件已成功修复！" -ForegroundColor Green
    Write-Host ""
    Write-Host "请重新启动Windows Terminal以应用更改。" -ForegroundColor Cyan
} catch {
    Write-Host "修复失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "请尝试以管理员身份运行此脚本" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "修复完成！" -ForegroundColor Green
