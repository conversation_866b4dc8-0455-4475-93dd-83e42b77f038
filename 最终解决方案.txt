Windows Terminal 最终解决方案
===============================

由于常规修复方法无效，请按照以下强力解决方案操作：

🚨 方案一：完全重置（推荐，成功率95%）
=====================================

步骤1：完全关闭Windows Terminal
-------------------------------
1. 按 Ctrl + Shift + Esc 打开任务管理器
2. 在"进程"选项卡中找到所有"Windows Terminal"进程
3. 选中并点击"结束任务"
4. 确保没有任何Terminal相关进程在运行

步骤2：删除所有设置数据
-----------------------
1. 按 Win + R，输入：%LOCALAPPDATA%\Packages
2. 找到以下文件夹（如果存在）：
   - Microsoft.WindowsTerminal_8wekyb3d8bbwe
   - Microsoft.WindowsTerminalPreview_8wekyb3d8bbwe
3. 进入每个文件夹的 LocalState 子文件夹
4. 删除 LocalState 文件夹中的所有内容（不要删除文件夹本身）

步骤3：清理注册表（可选）
-------------------------
1. 按 Win + R，输入：regedit
2. 导航到：HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\ApplicationAssociationToasts
3. 删除所有包含"WindowsTerminal"的项目

步骤4：重新启动
---------------
1. 重启计算机（重要！）
2. 启动后打开Windows Terminal
3. 系统会自动生成默认配置

🔧 方案二：PowerShell重置（需要管理员权限）
=========================================

1. 右键点击开始菜单，选择"Windows PowerShell (管理员)"
2. 运行以下命令：

# 重置Windows Terminal应用
Get-AppxPackage Microsoft.WindowsTerminal | Reset-AppxPackage

# 如果上面命令失败，尝试重新安装
Get-AppxPackage Microsoft.WindowsTerminal | Remove-AppxPackage
Get-AppxPackage -AllUsers Microsoft.WindowsTerminal | Foreach {Add-AppxPackage -DisableDevelopmentMode -Register "$($_.InstallLocation)\AppXManifest.xml"}

3. 重启计算机
4. 重新打开Windows Terminal

🛠️ 方案三：完全重新安装
========================

步骤1：卸载Windows Terminal
---------------------------
1. 打开"设置" > "应用"
2. 搜索"Windows Terminal"
3. 点击"卸载"

步骤2：清理残留文件
-------------------
1. 删除 %LOCALAPPDATA%\Packages\Microsoft.WindowsTerminal_8wekyb3d8bbwe
2. 删除 %LOCALAPPDATA%\Packages\Microsoft.WindowsTerminalPreview_8wekyb3d8bbwe

步骤3：重新安装
---------------
选择以下任一方式：
A. 从Microsoft Store安装
B. 从GitHub下载：https://github.com/microsoft/terminal/releases
C. 使用winget：winget install Microsoft.WindowsTerminal

🔍 方案四：系统级修复
====================

如果以上方案都无效，可能是系统问题：

1. 运行系统文件检查：
   sfc /scannow

2. 运行DISM修复：
   DISM /Online /Cleanup-Image /RestoreHealth

3. 检查Windows更新

4. 重置Windows应用商店：
   wsreset.exe

📋 应急配置文件
===============

如果需要手动创建配置文件，使用以下最小内容：

文件位置：%LOCALAPPDATA%\Packages\Microsoft.WindowsTerminal_8wekyb3d8bbwe\LocalState\settings.json

内容：
{
    "defaultProfile": "{61c54bbd-c2c6-5271-96e7-009a87ff44bf}",
    "profiles": {
        "list": [
            {
                "guid": "{61c54bbd-c2c6-5271-96e7-009a87ff44bf}",
                "name": "Windows PowerShell",
                "commandline": "powershell.exe"
            }
        ]
    }
}

⚠️ 重要提示
============

1. 在执行任何操作前，请确保已保存重要数据
2. 建议在非工作时间进行这些操作
3. 如果您的系统是企业管理的，可能需要IT管理员协助
4. 某些操作需要管理员权限

✅ 成功标志
===========

修复成功后，您应该看到：
- Windows Terminal正常启动
- 没有错误弹窗
- 可以正常使用PowerShell和命令提示符
- 设置界面可以正常打开

如果问题仍然存在，可能需要考虑：
1. 系统重装
2. 使用其他终端工具（如ConEmu、Hyper等）
3. 联系Microsoft技术支持
