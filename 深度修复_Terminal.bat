@echo off
chcp 65001 >nul
echo Windows Terminal 深度修复工具
echo =============================
echo.

echo 正在进行全面诊断和修复...
echo.

REM 设置变量
set "WT_PACKAGE=Microsoft.WindowsTerminal_8wekyb3d8bbwe"
set "WT_PREVIEW=Microsoft.WindowsTerminalPreview_8wekyb3d8bbwe"
set "SETTINGS_DIR1=%LOCALAPPDATA%\Packages\%WT_PACKAGE%\LocalState"
set "SETTINGS_DIR2=%LOCALAPPDATA%\Packages\%WT_PREVIEW%\LocalState"

echo 步骤1：查找Windows Terminal安装位置
echo ===================================
if exist "%SETTINGS_DIR1%" (
    echo 找到正式版：%SETTINGS_DIR1%
    set "ACTIVE_DIR=%SETTINGS_DIR1%"
    goto :found
)

if exist "%SETTINGS_DIR2%" (
    echo 找到预览版：%SETTINGS_DIR2%
    set "ACTIVE_DIR=%SETTINGS_DIR2%"
    goto :found
)

echo 错误：未找到Windows Terminal设置目录
echo 请确保Windows Terminal已正确安装
pause
exit /b 1

:found
echo.
echo 步骤2：备份和清理现有设置
echo =========================
set "SETTINGS_FILE=%ACTIVE_DIR%\settings.json"

if exist "%SETTINGS_FILE%" (
    echo 备份现有设置文件...
    copy "%SETTINGS_FILE%" "%SETTINGS_FILE%.backup_%date:~0,4%%date:~5,2%%date:~8,2%" >nul 2>&1
    echo 删除损坏的设置文件...
    del "%SETTINGS_FILE%" >nul 2>&1
)

REM 清理可能的临时文件
if exist "%ACTIVE_DIR%\state.json" (
    echo 清理状态文件...
    del "%ACTIVE_DIR%\state.json" >nul 2>&1
)

echo.
echo 步骤3：创建最小化配置
echo =====================
echo 正在创建新的设置文件...

(
echo {
echo     "$schema": "https://aka.ms/terminal-profiles-schema",
echo     "defaultProfile": "{61c54bbd-c2c6-5271-96e7-009a87ff44bf}",
echo     "profiles": {
echo         "list": [
echo             {
echo                 "guid": "{61c54bbd-c2c6-5271-96e7-009a87ff44bf}",
echo                 "name": "Windows PowerShell",
echo                 "commandline": "powershell.exe",
echo                 "hidden": false
echo             },
echo             {
echo                 "guid": "{0caa0dad-35be-5f56-a8ff-afceeeaa6101}",
echo                 "name": "命令提示符",
echo                 "commandline": "cmd.exe",
echo                 "hidden": false
echo             }
echo         ]
echo     }
echo }
) > "%SETTINGS_FILE%"

if exist "%SETTINGS_FILE%" (
    echo 新设置文件创建成功！
) else (
    echo 创建设置文件失败，可能是权限问题
    echo 请尝试以管理员身份运行此脚本
)

echo.
echo 步骤4：重置Windows Terminal缓存
echo ===============================
echo 正在清理应用缓存...

REM 清理应用缓存
if exist "%LOCALAPPDATA%\Packages\%WT_PACKAGE%\AC" (
    rd /s /q "%LOCALAPPDATA%\Packages\%WT_PACKAGE%\AC" >nul 2>&1
)

if exist "%LOCALAPPDATA%\Packages\%WT_PACKAGE%\TempState" (
    rd /s /q "%LOCALAPPDATA%\Packages\%WT_PACKAGE%\TempState" >nul 2>&1
)

echo.
echo 步骤5：验证修复结果
echo ===================
if exist "%SETTINGS_FILE%" (
    echo ✓ 设置文件已创建
    echo ✓ 缓存已清理
    echo.
    echo 修复完成！
    echo.
    echo 请按照以下步骤测试：
    echo 1. 完全关闭所有Windows Terminal窗口
    echo 2. 等待5秒钟
    echo 3. 重新打开Windows Terminal
    echo 4. 检查是否还有错误提示
    echo.
) else (
    echo ✗ 修复失败
    echo.
    echo 可能的解决方案：
    echo 1. 以管理员身份运行此脚本
    echo 2. 检查Windows Terminal是否正在运行并关闭它
    echo 3. 重新安装Windows Terminal
    echo.
)

echo 设置文件位置：%SETTINGS_FILE%
echo.
pause
