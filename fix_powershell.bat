@echo off
echo 正在修复PowerShell配置问题...
echo.

echo 1. 设置执行策略...
powershell -NoProfile -Command "Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser -Force"

echo.
echo 2. 备份并重置配置文件...
powershell -NoProfile -Command "if (Test-Path $PROFILE) { Rename-Item $PROFILE \"$PROFILE.backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')\" }"

echo.
echo 3. 创建配置文件目录...
powershell -NoProfile -Command "$profileDir = Split-Path $PROFILE -Parent; if (!(Test-Path $profileDir)) { New-Item -ItemType Directory -Path $profileDir -Force }"

echo.
echo 4. 创建新的配置文件...
powershell -NoProfile -Command "@'
# PowerShell 配置文件
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
function prompt { \"PS \" + (Get-Location) + \"> \" }
Write-Host \"PowerShell 配置文件已加载\" -ForegroundColor Green
'@ | Out-File -FilePath $PROFILE -Encoding UTF8"

echo.
echo 5. 清理PowerShell缓存...
if exist "%LOCALAPPDATA%\Microsoft\Windows\PowerShell" (
    rd /s /q "%LOCALAPPDATA%\Microsoft\Windows\PowerShell" 2>nul
)
if exist "%APPDATA%\Microsoft\Windows\PowerShell" (
    rd /s /q "%APPDATA%\Microsoft\Windows\PowerShell" 2>nul
)

echo.
echo 修复完成！
echo 请关闭当前PowerShell窗口并重新打开。
echo.
pause
