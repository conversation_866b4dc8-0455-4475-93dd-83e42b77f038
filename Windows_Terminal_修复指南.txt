Windows Terminal "加载用户设置时遇到错误" 修复指南
=====================================================

问题分析：
您的Windows Terminal设置文件(settings.json)存在以下问题：
1. 重复的Ubuntu配置项
2. 可能的JSON格式错误
3. 不兼容的配置选项

修复步骤：
=========

方法一：自动修复（推荐）
-----------------------
1. 双击运行 fix_terminal_settings.bat
2. 或者在PowerShell中运行：
   powershell -NoProfile -ExecutionPolicy Bypass -File "fix_terminal_settings.ps1"

方法二：手动修复
----------------
1. 按 Win + R，输入：%LOCALAPPDATA%
2. 导航到：Packages\Microsoft.WindowsTerminal_8wekyb3d8bbwe\LocalState\
3. 备份当前的 settings.json 文件
4. 用 settings_fixed.json 替换 settings.json
5. 重新启动Windows Terminal

方法三：重置为默认设置
---------------------
1. 完全关闭Windows Terminal
2. 删除或重命名 settings.json 文件
3. 重新启动Windows Terminal（会自动生成默认设置）

主要修复内容：
=============
✓ 移除了重复的Ubuntu配置项
✓ 简化了actions配置，使用直接的keys绑定
✓ 添加了默认字体设置
✓ 添加了startingDirectory设置
✓ 包含了基本的颜色方案
✓ 修复了JSON格式问题

修复后的功能：
=============
- Windows PowerShell
- 命令提示符
- Azure Cloud Shell
- Ubuntu (WSL)
- 基本快捷键：Ctrl+C (复制), Ctrl+V (粘贴), Ctrl+Shift+F (查找)
- 分屏功能：Alt+Shift+D

如果修复后仍有问题：
==================
1. 确保Windows Terminal已完全关闭
2. 重新启动计算机
3. 检查Windows Terminal是否为最新版本
4. 尝试重新安装Windows Terminal

需要帮助？
=========
如果问题仍然存在，请提供：
1. Windows Terminal版本号
2. 具体的错误信息
3. Windows版本信息
