document.addEventListener("DOMContentLoaded", function () {
    const carousel = document.getElementById("carousel");
    const images = carousel.getElementsByTagName("img");
    let currentIndex = 0;

    function showNextImage() {
        // 当前图片变为 inactive
        images[currentIndex].classList.remove("active");
        images[currentIndex].classList.add("inactive");

        // 下一张图片
        currentIndex = (currentIndex + 1) % images.length;

        // 显示下一张图片
        images[currentIndex].classList.add("active");
        images[currentIndex].classList.remove("inactive");
    }

    // 每 3 秒切换图片
    setInterval(showNextImage, 3000);
});
