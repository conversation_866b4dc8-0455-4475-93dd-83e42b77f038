PowerShell "无法加载设置" 错误修复指南
===========================================

您的PowerShell出现"无法加载设置"错误，这通常是由于配置文件损坏或执行策略问题导致的。

请按照以下步骤手动修复：

方法一：使用无配置文件模式修复
--------------------------------
1. 按 Win + R 打开运行对话框
2. 输入：powershell -NoProfile
3. 在打开的PowerShell窗口中依次执行以下命令：

# 设置执行策略
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser -Force

# 检查配置文件位置
$PROFILE

# 备份现有配置文件（如果存在）
if (Test-Path $PROFILE) {
    Rename-Item $PROFILE "$PROFILE.backup"
}

# 创建配置文件目录
$profileDir = Split-Path $PROFILE -Parent
if (!(Test-Path $profileDir)) {
    New-Item -ItemType Directory -Path $profileDir -Force
}

# 创建新的基本配置文件
@'
# PowerShell 配置文件
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
function prompt { "PS " + (Get-Location) + "> " }
'@ | Out-File -FilePath $PROFILE -Encoding UTF8

方法二：清理缓存
----------------
1. 关闭所有PowerShell窗口
2. 按 Win + R，输入：%LOCALAPPDATA%
3. 找到并删除 Microsoft\Windows\PowerShell 文件夹
4. 按 Win + R，输入：%APPDATA%
5. 找到并删除 Microsoft\Windows\PowerShell 文件夹

方法三：注册表修复（如果上述方法无效）
------------------------------------
1. 按 Win + R，输入：regedit
2. 导航到：HKEY_CURRENT_USER\Software\Microsoft\PowerShell\1\ShellIds\Microsoft.PowerShell
3. 找到 ExecutionPolicy 项，将其值设置为：RemoteSigned
4. 如果该项不存在，右键创建新的字符串值，名称为 ExecutionPolicy，值为 RemoteSigned

完成修复后：
-----------
1. 关闭所有PowerShell窗口
2. 重新打开PowerShell
3. 如果问题仍然存在，请以管理员身份运行PowerShell

如果您需要我帮助执行这些步骤，请告诉我！
