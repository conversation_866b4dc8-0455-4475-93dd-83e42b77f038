# PowerShell修复脚本
Write-Host "开始修复PowerShell配置问题..." -ForegroundColor Green

# 1. 检查PowerShell版本
Write-Host "`n1. 检查PowerShell版本:" -ForegroundColor Yellow
$PSVersionTable.PSVersion

# 2. 检查当前执行策略
Write-Host "`n2. 检查执行策略:" -ForegroundColor Yellow
$currentPolicy = Get-ExecutionPolicy
Write-Host "当前执行策略: $currentPolicy"

# 3. 如果执行策略过于严格，设置为RemoteSigned
if ($currentPolicy -eq "Restricted" -or $currentPolicy -eq "AllSigned") {
    Write-Host "设置执行策略为RemoteSigned..." -ForegroundColor Yellow
    try {
        Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser -Force
        Write-Host "执行策略已更新为RemoteSigned" -ForegroundColor Green
    } catch {
        Write-Host "无法更改执行策略: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 4. 检查配置文件路径
Write-Host "`n3. 检查PowerShell配置文件:" -ForegroundColor Yellow
Write-Host "配置文件路径: $PROFILE"

# 5. 检查配置文件是否存在
if (Test-Path $PROFILE) {
    Write-Host "配置文件存在，正在备份..." -ForegroundColor Yellow
    $backupPath = "$PROFILE.backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
    try {
        Copy-Item $PROFILE $backupPath
        Write-Host "配置文件已备份到: $backupPath" -ForegroundColor Green
        
        # 重命名原配置文件
        Rename-Item $PROFILE "$PROFILE.old"
        Write-Host "原配置文件已重命名为: $PROFILE.old" -ForegroundColor Green
    } catch {
        Write-Host "备份配置文件时出错: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "配置文件不存在" -ForegroundColor Green
}

# 6. 确保配置文件目录存在
$profileDir = Split-Path $PROFILE -Parent
if (!(Test-Path $profileDir)) {
    Write-Host "`n4. 创建配置文件目录..." -ForegroundColor Yellow
    try {
        New-Item -ItemType Directory -Path $profileDir -Force | Out-Null
        Write-Host "配置文件目录已创建: $profileDir" -ForegroundColor Green
    } catch {
        Write-Host "创建配置文件目录时出错: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 7. 创建新的基本配置文件
Write-Host "`n5. 创建新的配置文件..." -ForegroundColor Yellow
$newProfileContent = @"
# PowerShell 配置文件
# 自动生成于 $(Get-Date)

# 设置控制台编码为UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# 基本提示符设置
function prompt {
    "PS " + (Get-Location) + "> "
}

Write-Host "PowerShell 配置文件已加载" -ForegroundColor Green
"@

try {
    $newProfileContent | Out-File -FilePath $PROFILE -Encoding UTF8
    Write-Host "新配置文件已创建" -ForegroundColor Green
} catch {
    Write-Host "创建配置文件时出错: $($_.Exception.Message)" -ForegroundColor Red
}

# 8. 清理PowerShell缓存
Write-Host "`n6. 清理PowerShell缓存..." -ForegroundColor Yellow
$cacheLocations = @(
    "$env:LOCALAPPDATA\Microsoft\Windows\PowerShell",
    "$env:APPDATA\Microsoft\Windows\PowerShell"
)

foreach ($location in $cacheLocations) {
    if (Test-Path $location) {
        try {
            Get-ChildItem $location -Recurse | Remove-Item -Force -Recurse -ErrorAction SilentlyContinue
            Write-Host "已清理缓存: $location" -ForegroundColor Green
        } catch {
            Write-Host "清理缓存时出错 ($location): $($_.Exception.Message)" -ForegroundColor Yellow
        }
    }
}

# 9. 测试新配置
Write-Host "`n7. 测试PowerShell配置..." -ForegroundColor Yellow
try {
    # 重新加载配置文件
    if (Test-Path $PROFILE) {
        . $PROFILE
        Write-Host "配置文件加载成功!" -ForegroundColor Green
    }
} catch {
    Write-Host "加载配置文件时出错: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n修复完成!" -ForegroundColor Green
Write-Host "请关闭当前PowerShell窗口并重新打开以应用更改。" -ForegroundColor Cyan
Write-Host "如果问题仍然存在，请尝试以管理员身份运行PowerShell。" -ForegroundColor Cyan
