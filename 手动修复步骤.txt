Windows Terminal 手动修复步骤
===============================

由于自动修复脚本遇到问题，请按照以下步骤手动修复：

步骤1：找到设置文件位置
-----------------------
1. 按 Win + R 打开运行对话框
2. 输入：%LOCALAPPDATA%
3. 按回车，打开本地应用数据文件夹
4. 依次进入文件夹：
   Packages → Microsoft.WindowsTerminal_8wekyb3d8bbwe → LocalState
5. 在这个文件夹中找到 settings.json 文件

步骤2：备份当前设置
-------------------
1. 右键点击 settings.json 文件
2. 选择"复制"
3. 在同一文件夹中右键选择"粘贴"
4. 将复制的文件重命名为 settings_backup.json

步骤3：替换设置文件
-------------------
1. 找到我为您创建的 settings_fixed.json 文件
2. 复制 settings_fixed.json 文件
3. 粘贴到 LocalState 文件夹中
4. 将 settings_fixed.json 重命名为 settings.json
5. 如果提示替换文件，选择"是"

步骤4：重启Windows Terminal
---------------------------
1. 完全关闭所有Windows Terminal窗口
2. 重新打开Windows Terminal
3. 检查是否还有错误提示

如果上述步骤无效，尝试以下方法：
===============================

方法A：重置为默认设置
---------------------
1. 删除或重命名 settings.json 文件
2. 重新启动Windows Terminal
3. 系统会自动生成默认设置文件

方法B：使用记事本编辑
---------------------
1. 右键点击 settings.json 文件
2. 选择"打开方式" → "记事本"
3. 删除所有内容
4. 复制以下基本配置并粘贴：

{
    "$schema": "https://aka.ms/terminal-profiles-schema",
    "defaultProfile": "{61c54bbd-c2c6-5271-96e7-009a87ff44bf}",
    "profiles": {
        "list": [
            {
                "guid": "{61c54bbd-c2c6-5271-96e7-009a87ff44bf}",
                "name": "Windows PowerShell",
                "commandline": "powershell.exe",
                "hidden": false
            },
            {
                "guid": "{0caa0dad-35be-5f56-a8ff-afceeeaa6101}",
                "name": "命令提示符",
                "commandline": "cmd.exe",
                "hidden": false
            }
        ]
    }
}

5. 保存文件（Ctrl + S）
6. 重新启动Windows Terminal

常见问题解决：
=============

Q: 找不到设置文件夹？
A: 可能Windows Terminal安装在不同位置，尝试搜索：
   - Microsoft.WindowsTerminalPreview_8wekyb3d8bbwe
   - 或在开始菜单搜索"Windows Terminal"右键选择"打开文件位置"

Q: 没有权限修改文件？
A: 右键点击文件夹，选择"属性" → "安全" → "编辑"，给当前用户完全控制权限

Q: 修复后仍有问题？
A: 1. 检查Windows Terminal版本是否最新
   2. 重新安装Windows Terminal
   3. 重启计算机

需要帮助？
=========
如果按照上述步骤仍无法解决问题，请提供：
1. 具体的错误信息
2. Windows版本
3. Windows Terminal版本
