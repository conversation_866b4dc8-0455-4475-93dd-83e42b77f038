Windows Terminal 详细修复指南
===============================

🔍 问题诊断：
您的Windows Terminal出现"加载用户设置时遇到错误"，这是由于settings.json配置文件损坏导致的。

📋 修复步骤（请按顺序执行）：

步骤1：打开设置文件位置
-----------------------
方法A（推荐）：
1. 按键盘上的 Win键 + R键
2. 在弹出的"运行"对话框中输入：
   %LOCALAPPDATA%\Packages\Microsoft.WindowsTerminal_8wekyb3d8bbwe\LocalState
3. 按回车键

方法B（如果方法A找不到文件夹）：
1. 按键盘上的 Win键 + R键
2. 输入：%LOCALAPPDATA%
3. 按回车，然后依次双击进入：
   Packages → Microsoft.WindowsTerminal_8wekyb3d8bbwe → LocalState

步骤2：备份当前设置
-------------------
1. 在LocalState文件夹中找到 settings.json 文件
2. 右键点击 settings.json
3. 选择"复制"
4. 在空白处右键选择"粘贴"
5. 将新复制的文件重命名为 settings_backup.json

步骤3：应用修复
---------------
选择以下任一方法：

方法A - 使用我提供的修复文件：
1. 找到我创建的 settings_simple.json 文件
2. 复制该文件到LocalState文件夹
3. 删除原来的 settings.json 文件
4. 将 settings_simple.json 重命名为 settings.json

方法B - 手动创建新配置：
1. 删除原来的 settings.json 文件
2. 右键在空白处选择"新建" → "文本文档"
3. 将新文档重命名为 settings.json
4. 双击打开，复制以下内容并粘贴：

{
    "$schema": "https://aka.ms/terminal-profiles-schema",
    "defaultProfile": "{61c54bbd-c2c6-5271-96e7-009a87ff44bf}",
    "profiles": {
        "list": [
            {
                "guid": "{61c54bbd-c2c6-5271-96e7-009a87ff44bf}",
                "name": "Windows PowerShell",
                "commandline": "powershell.exe",
                "hidden": false
            },
            {
                "guid": "{0caa0dad-35be-5f56-a8ff-afceeeaa6101}",
                "name": "命令提示符",
                "commandline": "cmd.exe",
                "hidden": false
            }
        ]
    }
}

5. 保存文件（Ctrl + S）

步骤4：测试修复结果
-------------------
1. 完全关闭所有Windows Terminal窗口
2. 重新打开Windows Terminal
3. 检查是否还有错误提示

🚨 如果仍有问题：

问题1：找不到设置文件夹
解决：尝试以下路径
- %LOCALAPPDATA%\Packages\Microsoft.WindowsTerminalPreview_8wekyb3d8bbwe\LocalState
- 在开始菜单搜索"Windows Terminal"，右键选择"打开文件位置"

问题2：没有权限修改文件
解决：
1. 右键点击LocalState文件夹
2. 选择"属性"
3. 点击"安全"选项卡
4. 点击"编辑"
5. 选择您的用户名
6. 勾选"完全控制"
7. 点击"确定"

问题3：修复后仍有错误
解决：
1. 重新安装Windows Terminal
2. 重启计算机
3. 检查Windows更新

📞 需要进一步帮助？
如果按照上述步骤仍无法解决，请提供：
1. 您当前执行到了哪一步
2. 遇到的具体错误信息
3. 您的Windows版本（Win + R，输入winver查看）

✅ 修复成功的标志：
- Windows Terminal正常启动
- 没有错误弹窗
- 可以正常使用PowerShell和命令提示符
