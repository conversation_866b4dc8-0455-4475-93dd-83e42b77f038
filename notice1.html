<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>测绘学院推选优秀硕士学位论文公示</title>
    <link rel="stylesheet" type="text/css" href="css/style.css"> <!-- 引入全局样式 -->
    <style>
        /* 页面背景样式 */
        body {
            font-family: Arial, sans-serif;
            line-height: 1.8;
            margin: 0;
            background-color: #ffffff; /* 白色背景 */
            padding: 0;
        }

        /* 顶部横幅样式 */
        .top-banner {
            width: 100%;
            background-color: #00264d;
            color: white;
            text-align: center;
        }

        .top-banner img {
            width: 100%;
            height: auto;
        }

        /* 导航栏样式 */
        nav {
            background-color: #004080;
            padding: 10px 0;
            display: flex;
            justify-content: center;
        }

        nav ul {
            list-style-type: none;
            margin: 0;
            padding: 0;
            display: flex;
        }

        nav ul li {
            margin: 0 15px;
        }

        nav ul li a {
            text-decoration: none;
            color: white;
            font-weight: bold;
            font-size: 1rem;
        }

        nav ul li a:hover {
            text-decoration: underline;
        }

        /* 主容器样式 */
        .content-container {
            background-color: rgba(255, 255, 255, 0.9); /* 半透明白色背景 */
            color: #000; /* 黑色文字 */
            padding: 20px;
            margin: 20px auto;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.2); /* 添加阴影 */
            max-width: 800px;
        }

        h1 {
            color: #d71920; /* 红色标题 */
            font-size: 2em;
            text-align: center;
            margin-bottom: 20px;
        }

        .info {
            text-align: right;
            font-size: 0.9em;
            margin-bottom: 20px;
            color: #333;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 1rem;
        }

        table th, table td {
            border: 1px solid #ccc;
            padding: 10px;
            text-align: center;
        }

        table th {
            background-color: #f4f4f4;
            font-weight: bold;
        }

        table tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        table tr:hover {
            background-color: #f0f0f0;
        }

        /* 页脚样式 */
        footer {
            width: 60%;
            max-width: 1000px;
            text-align: center;
            padding: 20px;
            background-color: #00264d;
            color: white; /* 确保字体颜色为白色 */
            margin: 20px auto;
            border-radius: 5px;
            box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }

        footer p {
            margin: 5px 0;
            font-size: 0.9rem;
            line-height: 1.5;
            color: white; /* 确保段落中的文字为白色 */
        }
    </style>
</head>
<body>
    <!-- 顶部横幅 -->
    <div class="top-banner">
        <img src="images/5.png" alt="顶部横幅">
    </div>

    <!-- 导航栏 -->
    <nav>
        <ul>
            <li><a href="index.html">首页</a></li>
            <li><a href="about.html">学院概况</a></li>
            <li><a href="undergraduate.html">本科生培养</a></li>
            <li><a href="postgraduate.html">研究生培养</a></li>
            <li><a href="research.html">科学研究</a></li>
            <li><a href="employment.html">招生就业</a></li>
            <li><a href="student.html">学生工作</a></li>
        </ul>
    </nav>

    <!-- 主内容部分 -->
    <div class="content-container">
        <h1>测绘与空间信息工程学院推选江西省优秀硕士学位论文名单公示</h1>
        <div class="info">
            发布者：张家荣&nbsp;&nbsp;&nbsp;&nbsp;发布时间：2024-10-14&nbsp;&nbsp;&nbsp;&nbsp;浏览次数：887
        </div>
        <p>
            根据江西省人民政府学位委员会办公室和我校相关要求，经测绘与空间信息工程学院学位评定分委员会审议推选，现将2023年和2024年全省优秀硕士学位论文推选名单予以公示。
        </p>
        <p>
            公示时间：2024年10月14日至2024年10月16日（3个工作日）
        </p>

        <h2>2023年全省优秀硕士学位论文推选名单</h2>
        <table>
            <thead>
                <tr>
                    <th>序号</th>
                    <th>作者姓名</th>
                    <th>导师姓名</th>
                    <th>专业名称及代码</th>
                    <th>学位论文题目</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>1</td>
                    <td>陈涛</td>
                    <td>王乐洋</td>
                    <td>测绘科学与技术081600</td>
                    <td>病态加性条件混合型最差模型参数估计方法研究</td>
                </tr>
                <tr>
                    <td>2</td>
                    <td>聂晶</td>
                    <td>李小龙</td>
                    <td>测绘工程085215</td>
                    <td>顾及时空约束的大型铁路干线报桩推荐模型研究</td>
                </tr>
            </tbody>
        </table>

        <h2>2024年全省优秀硕士学位论文推选名单</h2>
        <table>
            <thead>
                <tr>
                    <th>序号</th>
                    <th>作者姓名</th>
                    <th>导师姓名</th>
                    <th>专业名称及代码</th>
                    <th>学位论文题目</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>1</td>
                    <td>张文典</td>
                    <td>陈本富</td>
                    <td>测绘科学与技术081600</td>
                    <td>基于三维模型的光谱语义观测网络智能划分方法</td>
                </tr>
                <tr>
                    <td>2</td>
                    <td>孟祥龙</td>
                    <td>叶发茂</td>
                    <td>地理空间信息系统070503</td>
                    <td>基于改建铁路沿线自动成品点云信息提取技术研究</td>
                </tr>
                <tr>
                    <td>3</td>
                    <td>侯阳阳</td>
                    <td>吕开云</td>
                    <td>测绘科学与技术081600</td>
                    <td>基于多尺度变换和改进时域神经网络的多源遥感影像融合</td>
                </tr>
            </tbody>
        </table>
    </div>

    <!-- 页脚 -->
    <footer>
        东华理工大学测绘与空间信息工程学院<br>
        2024年10月
    </footer>
</body>
</html>
