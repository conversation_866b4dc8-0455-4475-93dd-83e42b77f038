/* 设置背景图片样式 */
body {
    background-image: url("../images/6.jpg"); /* 背景图片路径 */
    background-size: cover; /* 背景图片完全覆盖页面 */
    background-repeat: no-repeat; /* 背景图片不重复 */
    background-attachment: fixed; /* 背景图片固定不随页面滚动 */
    background-position: center; /* 背景图片居中显示 */
    margin: 0; /* 移除 body 的默认边距 */
    padding: 0; /* 移除 body 的默认内边距 */
    box-sizing: border-box; /* 全局设置边框盒模型 */
}

/* 顶部横幅样式 */
.top-banner {
    width: 100%; /* 横幅宽度设置为100%以覆盖页面宽度 */
    height: auto;
    margin: 0; /* 移除任何上下外边距 */
    padding: 0; /* 移除任何内边距 */
    background: none; /* 确保没有背景颜色 */
    border: none; /* 确保没有边框 */
    position: relative; /* 为横幅设置相对定位 */
}

.top-banner img {
    display: block; /* 移除图片默认的 inline 间距 */
    width: 100%; /* 图片宽度填充整个横幅 */
    height: auto; /* 按比例缩放图片 */
    margin: 0; /* 移除图片的外边距 */
    padding: 0; /* 移除图片的内边距 */
    border: none; /* 移除图片边框 */
    box-shadow: none; /* 移除任何阴影效果 */
}

/* 导航栏样式 */
header {
    width: 100%; /* 调整导航栏宽度与页面一致 */
    max-width: 1000px;
    margin: 0 auto;
    background-color: #00264d;
    border-radius: 5px;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
    padding: 0;
}

header nav ul {
    list-style-type: none;
    padding: 0;
    margin: 0;
    display: flex;
    justify-content: space-around;
}

header nav ul li {
    padding: 15px;
}

header nav ul li a {
    color: white;
    text-decoration: none;
    font-weight: bold;
}

/* 图片轮播样式 */
.carousel {
    width: 60%;
    max-width: 1000px;
    height: 500px; /* 调整高度，确保图片完整显示 */
    margin: 10px auto;
    overflow: hidden;
    border-radius: 5px;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
    position: relative;
}

.carousel img {
    position: absolute;
    top: 0;
    left: 100%;
    width: 100%;
    height: 100%;
    object-fit: cover; /* 保证图片比例一致 */
    transition: left 1s ease-in-out; /* 动画效果 */
}

.carousel img.active {
    left: 0;
}

.carousel img.inactive {
    left: -100%;
}

/* 主体内容样式 */
main {
    width: 60%;
    max-width: 1000px;
    margin: 20px auto;
    display: flex;
    justify-content: space-between;
    gap: 20px;
}

section {
    width: 48%;
    background-color: #f9f9f9;
    padding: 15px;
    border-radius: 5px;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
}

section h2 {
    color: #004080;
    margin-bottom: 10px;
}

section ul {
    list-style-type: none;
    padding: 0;
    margin: 0;
}

section ul li {
    margin-bottom: 10px;
}

section ul li a {
    text-decoration: none;
    color: #004080;
}

section ul li a:hover {
    text-decoration: underline;
}

/* 页脚样式 */
footer {
    width: 60%;
    max-width: 1000px;
    text-align: center;
    padding: 10px;
    background-color: #00264d;
    color: white;
    margin: 20px auto;
    border-radius: 5px;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
}

