@echo off
echo PowerShell 修复工具
echo ==================
echo.

echo 步骤 1: 重置执行策略...
reg add "HKCU\Software\Microsoft\PowerShell\1\ShellIds\Microsoft.PowerShell" /v ExecutionPolicy /t REG_SZ /d RemoteSigned /f >nul 2>&1

echo 步骤 2: 删除损坏的配置文件...
if exist "%USERPROFILE%\Documents\WindowsPowerShell\Microsoft.PowerShell_profile.ps1" (
    ren "%USERPROFILE%\Documents\WindowsPowerShell\Microsoft.PowerShell_profile.ps1" "Microsoft.PowerShell_profile.ps1.backup" >nul 2>&1
)

if exist "%USERPROFILE%\Documents\PowerShell\Microsoft.PowerShell_profile.ps1" (
    ren "%USERPROFILE%\Documents\PowerShell\Microsoft.PowerShell_profile.ps1" "Microsoft.PowerShell_profile.ps1.backup" >nul 2>&1
)

echo 步骤 3: 清理缓存目录...
if exist "%LOCALAPPDATA%\Microsoft\Windows\PowerShell" (
    rd /s /q "%LOCALAPPDATA%\Microsoft\Windows\PowerShell" >nul 2>&1
)

if exist "%APPDATA%\Microsoft\Windows\PowerShell" (
    rd /s /q "%APPDATA%\Microsoft\Windows\PowerShell" >nul 2>&1
)

echo 步骤 4: 创建配置目录...
if not exist "%USERPROFILE%\Documents\WindowsPowerShell" (
    md "%USERPROFILE%\Documents\WindowsPowerShell" >nul 2>&1
)

if not exist "%USERPROFILE%\Documents\PowerShell" (
    md "%USERPROFILE%\Documents\PowerShell" >nul 2>&1
)

echo 步骤 5: 创建基本配置文件...
echo # PowerShell Profile > "%USERPROFILE%\Documents\WindowsPowerShell\Microsoft.PowerShell_profile.ps1"
echo [Console]::OutputEncoding = [System.Text.Encoding]::UTF8 >> "%USERPROFILE%\Documents\WindowsPowerShell\Microsoft.PowerShell_profile.ps1"

echo # PowerShell Profile > "%USERPROFILE%\Documents\PowerShell\Microsoft.PowerShell_profile.ps1"
echo [Console]::OutputEncoding = [System.Text.Encoding]::UTF8 >> "%USERPROFILE%\Documents\PowerShell\Microsoft.PowerShell_profile.ps1"

echo.
echo 修复完成！
echo.
echo 请按照以下步骤操作：
echo 1. 关闭所有PowerShell窗口
echo 2. 重新打开PowerShell
echo 3. 如果问题仍然存在，请以管理员身份运行PowerShell
echo.
pause
