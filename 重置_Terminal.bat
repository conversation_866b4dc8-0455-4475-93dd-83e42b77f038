@echo off
echo Windows Terminal 完全重置工具
echo =============================
echo.
echo 警告：此操作将完全重置Windows Terminal设置
echo 请确保已关闭所有Windows Terminal窗口
echo.
pause

echo 正在重置Windows Terminal...
echo.

REM 方法1：删除设置文件让系统重新生成
echo 步骤1：删除用户设置文件
for %%p in (Microsoft.WindowsTerminal_8wekyb3d8bbwe Microsoft.WindowsTerminalPreview_8wekyb3d8bbwe) do (
    if exist "%LOCALAPPDATA%\Packages\%%p\LocalState\settings.json" (
        echo 删除 %%p 的设置文件...
        del "%LOCALAPPDATA%\Packages\%%p\LocalState\settings.json" >nul 2>&1
        del "%LOCALAPPDATA%\Packages\%%p\LocalState\state.json" >nul 2>&1
    )
)

REM 方法2：清理应用数据
echo.
echo 步骤2：清理应用数据
for %%p in (Microsoft.WindowsTerminal_8wekyb3d8bbwe Microsoft.WindowsTerminalPreview_8wekyb3d8bbwe) do (
    if exist "%LOCALAPPDATA%\Packages\%%p" (
        echo 清理 %%p 的缓存...
        if exist "%LOCALAPPDATA%\Packages\%%p\AC" rd /s /q "%LOCALAPPDATA%\Packages\%%p\AC" >nul 2>&1
        if exist "%LOCALAPPDATA%\Packages\%%p\TempState" rd /s /q "%LOCALAPPDATA%\Packages\%%p\TempState" >nul 2>&1
    )
)

REM 方法3：重置应用（需要管理员权限）
echo.
echo 步骤3：尝试重置应用注册
powershell -Command "Get-AppxPackage Microsoft.WindowsTerminal | Reset-AppxPackage" >nul 2>&1
if %errorlevel% equ 0 (
    echo 应用重置成功
) else (
    echo 应用重置失败（可能需要管理员权限）
)

echo.
echo 重置完成！
echo.
echo 请按照以下步骤测试：
echo 1. 等待10秒钟
echo 2. 打开Windows Terminal
echo 3. 系统会自动生成默认设置
echo.
echo 如果问题仍然存在，请尝试：
echo 1. 重启计算机
echo 2. 重新安装Windows Terminal
echo 3. 检查Windows更新
echo.
pause
