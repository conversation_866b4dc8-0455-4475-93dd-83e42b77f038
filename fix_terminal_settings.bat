@echo off
echo Windows Terminal 设置修复工具
echo ===============================
echo.

set "TERMINAL_SETTINGS=%LOCALAPPDATA%\Packages\Microsoft.WindowsTerminal_8wekyb3d8bbwe\LocalState\settings.json"
set "BACKUP_FILE=%LOCALAPPDATA%\Packages\Microsoft.WindowsTerminal_8wekyb3d8bbwe\LocalState\settings_backup_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%.json"

echo 正在检查Windows Terminal设置文件...

if exist "%TERMINAL_SETTINGS%" (
    echo 找到设置文件: %TERMINAL_SETTINGS%
    echo.
    echo 正在备份当前设置...
    copy "%TERMINAL_SETTINGS%" "%BACKUP_FILE%" >nul 2>&1
    if %errorlevel% equ 0 (
        echo 备份成功: %BACKUP_FILE%
    ) else (
        echo 备份失败，但继续修复...
    )
    echo.
    
    echo 正在应用修复后的设置...
    copy "settings_fixed.json" "%TERMINAL_SETTINGS%" >nul 2>&1
    if %errorlevel% equ 0 (
        echo 设置文件已修复！
        echo.
        echo 请重新启动Windows Terminal以应用更改。
    ) else (
        echo 修复失败，请检查权限或手动替换设置文件。
    )
) else (
    echo 未找到Windows Terminal设置文件。
    echo 可能的原因：
    echo 1. Windows Terminal未安装
    echo 2. 设置文件位置不同
    echo.
    echo 请手动将 settings_fixed.json 复制到以下位置：
    echo %TERMINAL_SETTINGS%
)

echo.
echo 如果问题仍然存在，请尝试：
echo 1. 完全关闭Windows Terminal
echo 2. 重新启动Windows Terminal
echo 3. 检查是否有其他Terminal进程在运行
echo.
pause
