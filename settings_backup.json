{"$help": "https://aka.ms/terminal-documentation", "$schema": "https://aka.ms/terminal-profiles-schema", "actions": [{"command": {"action": "copy", "singleLine": false}, "id": "User.copy.644BA8F2"}, {"command": "paste", "id": "User.paste"}, {"command": "find", "id": "User.find"}, {"command": {"action": "splitPane", "split": "auto", "splitMode": "duplicate"}, "id": "User.splitPane.********"}], "copyFormatting": "none", "copyOnSelect": false, "defaultProfile": "{61c54bbd-c2c6-5271-96e7-009a87ff44bf}", "keybindings": [{"id": "User.copy.644BA8F2", "keys": "ctrl+c"}, {"id": "User.find", "keys": "ctrl+shift+f"}, {"id": "User.paste", "keys": "ctrl+v"}, {"id": "User.splitPane.********", "keys": "alt+shift+d"}], "newTabMenu": [{"type": "remainingProfiles"}], "profiles": {"defaults": {}, "list": [{"commandline": "%SystemRoot%\\System32\\WindowsPowerShell\\v1.0\\powershell.exe", "guid": "{61c54bbd-c2c6-5271-96e7-009a87ff44bf}", "hidden": false, "name": "Windows PowerShell"}, {"commandline": "%SystemRoot%\\System32\\cmd.exe", "guid": "{0caa0dad-35be-5f56-a8ff-afceeeaa6101}", "hidden": false, "name": "命令提示符"}, {"guid": "{b453ae62-4e3d-5e58-b989-0a998ec441b8}", "hidden": false, "name": "Azure Cloud Shell", "source": "Windows.Terminal.Azure"}, {"guid": "{130032b1-7efa-5707-bdb9-d8fde275f186}", "hidden": false, "name": "Ubuntu", "source": "Microsoft.WSL"}, {"guid": "{32010eec-af4b-5a20-8c78-1dbe82e0928f}", "hidden": false, "name": "Ubuntu", "source": "Microsoft.WSL"}]}, "schemes": [], "themes": []}