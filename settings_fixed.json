{"$help": "https://aka.ms/terminal-documentation", "$schema": "https://aka.ms/terminal-profiles-schema", "actions": [{"command": {"action": "copy", "singleLine": false}, "keys": "ctrl+c"}, {"command": "paste", "keys": "ctrl+v"}, {"command": "find", "keys": "ctrl+shift+f"}, {"command": {"action": "splitPane", "split": "auto", "splitMode": "duplicate"}, "keys": "alt+shift+d"}], "copyFormatting": "none", "copyOnSelect": false, "defaultProfile": "{61c54bbd-c2c6-5271-96e7-009a87ff44bf}", "newTabMenu": [{"type": "remainingProfiles"}], "profiles": {"defaults": {"font": {"face": "Consolas"}}, "list": [{"commandline": "%SystemRoot%\\System32\\WindowsPowerShell\\v1.0\\powershell.exe", "guid": "{61c54bbd-c2c6-5271-96e7-009a87ff44bf}", "hidden": false, "name": "Windows PowerShell", "startingDirectory": "%USERPROFILE%"}, {"commandline": "%SystemRoot%\\System32\\cmd.exe", "guid": "{0caa0dad-35be-5f56-a8ff-afceeeaa6101}", "hidden": false, "name": "命令提示符", "startingDirectory": "%USERPROFILE%"}, {"guid": "{b453ae62-4e3d-5e58-b989-0a998ec441b8}", "hidden": false, "name": "Azure Cloud Shell", "source": "Windows.Terminal.Azure"}, {"guid": "{130032b1-7efa-5707-bdb9-d8fde275f186}", "hidden": false, "name": "Ubuntu", "source": "Microsoft.WSL"}]}, "schemes": [{"name": "<PERSON>", "foreground": "#CCCCCC", "background": "#0C0C0C", "cursorColor": "#FFFFFF", "black": "#0C0C0C", "red": "#C50F1F", "green": "#13A10E", "yellow": "#C19C00", "blue": "#0037DA", "purple": "#881798", "cyan": "#3A96DD", "white": "#CCCCCC", "brightBlack": "#767676", "brightRed": "#E74856", "brightGreen": "#16C60C", "brightYellow": "#F9F1A5", "brightBlue": "#3B78FF", "brightPurple": "#B4009E", "brightCyan": "#61D6D6", "brightWhite": "#F2F2F2"}], "themes": []}