@echo off
chcp 65001 >nul
echo Windows Terminal 简单修复工具
echo =============================
echo.

echo 正在查找Windows Terminal设置文件...
echo.

set "SETTINGS_DIR=%LOCALAPPDATA%\Packages\Microsoft.WindowsTerminal_8wekyb3d8bbwe\LocalState"
set "SETTINGS_FILE=%SETTINGS_DIR%\settings.json"

if not exist "%SETTINGS_DIR%" (
    echo 错误：未找到Windows Terminal设置目录
    echo 目录：%SETTINGS_DIR%
    echo.
    echo 请确保Windows Terminal已正确安装
    pause
    exit /b 1
)

if exist "%SETTINGS_FILE%" (
    echo 找到设置文件：%SETTINGS_FILE%
    echo.
    
    echo 正在备份当前设置...
    copy "%SETTINGS_FILE%" "%SETTINGS_FILE%.backup" >nul 2>&1
    if %errorlevel% equ 0 (
        echo 备份成功
    ) else (
        echo 备份失败，但继续修复...
    )
    echo.
) else (
    echo 设置文件不存在，将创建新文件
    echo.
)

echo 正在应用修复后的设置...
if exist "settings_fixed.json" (
    copy "settings_fixed.json" "%SETTINGS_FILE%" >nul 2>&1
    if %errorlevel% equ 0 (
        echo 修复成功！
        echo.
        echo 请完全关闭Windows Terminal，然后重新打开
        echo.
    ) else (
        echo 修复失败，可能是权限问题
        echo 请尝试以管理员身份运行此脚本
        echo.
    )
) else (
    echo 错误：未找到修复文件 settings_fixed.json
    echo 请确保该文件在同一目录下
    echo.
)

echo 修复完成
pause
