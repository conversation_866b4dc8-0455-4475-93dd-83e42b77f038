# Windows Terminal 深度修复脚本
Write-Host "Windows Terminal 深度修复工具" -ForegroundColor Green
Write-Host "=============================" -ForegroundColor Green
Write-Host ""

# 查找Windows Terminal安装位置
$wtPackages = @(
    "Microsoft.WindowsTerminal_8wekyb3d8bbwe",
    "Microsoft.WindowsTerminalPreview_8wekyb3d8bbwe"
)

$activePackage = $null
$settingsDir = $null

foreach ($package in $wtPackages) {
    $testPath = "$env:LOCALAPPDATA\Packages\$package\LocalState"
    if (Test-Path $testPath) {
        $activePackage = $package
        $settingsDir = $testPath
        Write-Host "找到Windows Terminal: $package" -ForegroundColor Yellow
        break
    }
}

if (-not $settingsDir) {
    Write-Host "错误：未找到Windows Terminal安装" -ForegroundColor Red
    Write-Host "请确保Windows Terminal已正确安装" -ForegroundColor Red
    Read-Host "按回车键退出"
    exit 1
}

$settingsFile = Join-Path $settingsDir "settings.json"
Write-Host "设置文件位置: $settingsFile" -ForegroundColor Cyan
Write-Host ""

# 步骤1：完全清理现有设置
Write-Host "步骤1：清理现有设置" -ForegroundColor Yellow
Write-Host "===================" -ForegroundColor Yellow

if (Test-Path $settingsFile) {
    $backupFile = "$settingsFile.backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
    try {
        Copy-Item $settingsFile $backupFile
        Write-Host "✓ 已备份到: $backupFile" -ForegroundColor Green
    } catch {
        Write-Host "⚠ 备份失败，但继续修复..." -ForegroundColor Yellow
    }
    
    try {
        Remove-Item $settingsFile -Force
        Write-Host "✓ 已删除损坏的设置文件" -ForegroundColor Green
    } catch {
        Write-Host "✗ 删除设置文件失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 清理其他可能的问题文件
$filesToClean = @("state.json", "ApplicationState.json")
foreach ($file in $filesToClean) {
    $filePath = Join-Path $settingsDir $file
    if (Test-Path $filePath) {
        try {
            Remove-Item $filePath -Force
            Write-Host "✓ 已清理: $file" -ForegroundColor Green
        } catch {
            Write-Host "⚠ 无法清理: $file" -ForegroundColor Yellow
        }
    }
}

Write-Host ""

# 步骤2：清理应用缓存
Write-Host "步骤2：清理应用缓存" -ForegroundColor Yellow
Write-Host "==================" -ForegroundColor Yellow

$cacheDirectories = @(
    "$env:LOCALAPPDATA\Packages\$activePackage\AC",
    "$env:LOCALAPPDATA\Packages\$activePackage\TempState",
    "$env:LOCALAPPDATA\Packages\$activePackage\Settings"
)

foreach ($cacheDir in $cacheDirectories) {
    if (Test-Path $cacheDir) {
        try {
            Remove-Item $cacheDir -Recurse -Force
            Write-Host "✓ 已清理缓存: $(Split-Path $cacheDir -Leaf)" -ForegroundColor Green
        } catch {
            Write-Host "⚠ 无法清理缓存: $(Split-Path $cacheDir -Leaf)" -ForegroundColor Yellow
        }
    }
}

Write-Host ""

# 步骤3：创建最小化配置
Write-Host "步骤3：创建新的配置文件" -ForegroundColor Yellow
Write-Host "========================" -ForegroundColor Yellow

$minimalConfig = @{
    '$schema' = 'https://aka.ms/terminal-profiles-schema'
    defaultProfile = '{61c54bbd-c2c6-5271-96e7-009a87ff44bf}'
    profiles = @{
        list = @(
            @{
                guid = '{61c54bbd-c2c6-5271-96e7-009a87ff44bf}'
                name = 'Windows PowerShell'
                commandline = 'powershell.exe'
                hidden = $false
            },
            @{
                guid = '{0caa0dad-35be-5f56-a8ff-afceeeaa6101}'
                name = '命令提示符'
                commandline = 'cmd.exe'
                hidden = $false
            }
        )
    }
}

try {
    $minimalConfig | ConvertTo-Json -Depth 10 | Out-File -FilePath $settingsFile -Encoding UTF8
    Write-Host "✓ 新配置文件创建成功" -ForegroundColor Green
} catch {
    Write-Host "✗ 创建配置文件失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "尝试手动创建..." -ForegroundColor Yellow
    
    # 手动创建基本配置
    $basicConfig = @'
{
    "$schema": "https://aka.ms/terminal-profiles-schema",
    "defaultProfile": "{61c54bbd-c2c6-5271-96e7-009a87ff44bf}",
    "profiles": {
        "list": [
            {
                "guid": "{61c54bbd-c2c6-5271-96e7-009a87ff44bf}",
                "name": "Windows PowerShell",
                "commandline": "powershell.exe",
                "hidden": false
            },
            {
                "guid": "{0caa0dad-35be-5f56-a8ff-afceeeaa6101}",
                "name": "命令提示符",
                "commandline": "cmd.exe",
                "hidden": false
            }
        ]
    }
}
'@
    
    try {
        $basicConfig | Out-File -FilePath $settingsFile -Encoding UTF8
        Write-Host "✓ 基本配置文件创建成功" -ForegroundColor Green
    } catch {
        Write-Host "✗ 仍然无法创建配置文件" -ForegroundColor Red
        Write-Host "请检查权限或以管理员身份运行" -ForegroundColor Red
    }
}

Write-Host ""

# 步骤4：验证修复
Write-Host "步骤4：验证修复结果" -ForegroundColor Yellow
Write-Host "==================" -ForegroundColor Yellow

if (Test-Path $settingsFile) {
    try {
        $content = Get-Content $settingsFile -Raw | ConvertFrom-Json
        Write-Host "✓ 配置文件格式正确" -ForegroundColor Green
        Write-Host "✓ 修复完成！" -ForegroundColor Green
    } catch {
        Write-Host "⚠ 配置文件可能有格式问题" -ForegroundColor Yellow
    }
} else {
    Write-Host "✗ 配置文件不存在" -ForegroundColor Red
}

Write-Host ""
Write-Host "修复完成！请按照以下步骤测试：" -ForegroundColor Cyan
Write-Host "1. 完全关闭所有Windows Terminal窗口" -ForegroundColor White
Write-Host "2. 等待5秒钟" -ForegroundColor White
Write-Host "3. 重新打开Windows Terminal" -ForegroundColor White
Write-Host "4. 检查是否还有错误提示" -ForegroundColor White
Write-Host ""
Write-Host "设置文件位置: $settingsFile" -ForegroundColor Gray
Write-Host ""

Read-Host "按回车键退出"
